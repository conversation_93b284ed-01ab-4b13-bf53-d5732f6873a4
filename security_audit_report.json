{"scan_summary": {"timestamp": "2025-05-26T21:29:16.331051", "files_scanned": 39, "total_vulnerabilities": 14, "severity_breakdown": {"CRITICAL": 8, "HIGH": 4, "MEDIUM": 2, "LOW": 0}}, "vulnerabilities_by_type": {"command_injection": [{"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\bugbounty_system.py", "line": 365, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    363:         $query = \"SELECT * FROM users WHERE username = '\" . $_POST['username'] . \"'\";\n    364:         document.getElementById('search-results').innerHTML = searchQuery;\n>>> 365:         exec(\"convert \" + uploadedFile + \" thumbnail.jpg\");\n    366:         if ($_SESSION['user_id'] == $_GET['user_id']) { /* access granted */ }\n    367:         \"\"\",", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\demo_complete_bugbounty.py", "line": 50, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "     48:             $query = \"SELECT * FROM users WHERE username = '\" . $_POST['username'] . \"'\";\n     49:             document.getElementById('output').innerHTML = userInput;\n>>>  50:             exec(\"convert \" + filename + \" output.jpg\");\n     51:             \"\"\",\n     52:             scope_notes=\"Full scope testing allowed, no DoS attacks\"", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 57, "type": "command_injection", "severity": "HIGH", "description": "Use of os.system() - potential command injection", "code_snippet": "     55:                     \"pattern\": r'os\\.system\\s*\\(',\n     56:                     \"severity\": \"HIGH\",\n>>>  57:                     \"description\": \"Use of os.system() - potential command injection\"\n     58:                 },\n     59:                 {", "pattern": "os\\.system\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 67, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "     65:                     \"pattern\": r'exec\\s*\\(',\n     66:                     \"severity\": \"CRITICAL\",\n>>>  67:                     \"description\": \"Use of exec() - code injection risk\"\n     68:                 },\n     69:                 {", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 357, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    355:         if \"dangerous_function\" in vuln_types:\n    356:             recommendations.append(\n>>> 357:                 \"🔧 Dangerous Functions: Избегайте использования eval(), exec() и подобных функций\"\n    358:             )\n    359:         ", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 72, "type": "command_injection", "severity": "CRITICAL", "description": "Use of eval() - code injection risk", "code_snippet": "     70:                     \"pattern\": r'eval\\s*\\(',\n     71:                     \"severity\": \"CRITICAL\", \n>>>  72:                     \"description\": \"Use of eval() - code injection risk\"\n     73:                 }\n     74:             ],", "pattern": "eval\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 357, "type": "command_injection", "severity": "CRITICAL", "description": "Use of eval() - code injection risk", "code_snippet": "    355:         if \"dangerous_function\" in vuln_types:\n    356:             recommendations.append(\n>>> 357:                 \"🔧 Dangerous Functions: Избегайте использования eval(), exec() и подобных функций\"\n    358:             )\n    359:         ", "pattern": "eval\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\vulnerability_hunter.py", "line": 499, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    497:         $query = \"SELECT * FROM users WHERE username = '\" . $_POST['username'] . \"'\";\n    498:         document.getElementById('output').innerHTML = userInput;\n>>> 499:         exec(\"convert \" + filename + \" output.jpg\");\n    500:         \"\"\"\n    501:     )", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\evaluator_agent\\agent.py", "line": 170, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    168:             logger.debug(f\"Executing code: {' '.join(cmd)} in {temp_dir}\")\n    169:             start_time = time.monotonic()\n>>> 170:             proc = await asyncio.create_subprocess_exec(\n    171:                 *cmd,\n    172:                 stdout=asyncio.subprocess.PIPE,", "pattern": "exec\\s*\\("}], "sql_injection": [{"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 38, "type": "sql_injection", "severity": "HIGH", "description": "Potential SQL injection via string concatenation", "code_snippet": "     36:             \"sql_injection\": [\n     37:                 {\n>>>  38:                     \"pattern\": r'[\"\\'].*\\+.*[\"\\'].*WHERE',\n     39:                     \"severity\": \"HIGH\",\n     40:                     \"description\": \"Potential SQL injection via string concatenation\"", "pattern": "[\"\\'].*\\+.*[\"\\'].*WHERE"}], "hardcoded_secrets": [{"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\config\\settings.py", "line": 19, "type": "hardcoded_secrets", "severity": "HIGH", "description": "Hardcoded API key detected", "code_snippet": "     17:     # In a real deployment, use environment variables, secrets management, or other secure methods.\n     18:     # For local testing without a .env file, you can temporarily set it like:\n>>>  19:     # GEMINI_API_KEY = \"YOUR_ACTUAL_API_KEY_HERE\"\n     20:     print(\"Warning: GEMINI_API_KEY not found in .env or environment. Using a NON-FUNCTIONAL placeholder. Please create a .env file with your valid API key.\")\n     21:     GEMINI_API_KEY = \"YOUR_API_KEY_FROM_DOTENV_WAS_NOT_FOUND_PLEASE_SET_IT_UP\" # Obvious placeholder", "pattern": "api_key\\s*=\\s*[\"\\'][^\"\\']{20,}[\"\\']"}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\config\\settings.py", "line": 21, "type": "hardcoded_secrets", "severity": "HIGH", "description": "Hardcoded API key detected", "code_snippet": "     19:     # GEMINI_API_KEY = \"YOUR_ACTUAL_API_KEY_HERE\"\n     20:     print(\"Warning: GEMINI_API_KEY not found in .env or environment. Using a NON-FUNCTIONAL placeholder. Please create a .env file with your valid API key.\")\n>>>  21:     GEMINI_API_KEY = \"YOUR_API_KEY_FROM_DOTENV_WAS_NOT_FOUND_PLEASE_SET_IT_UP\" # Obvious placeholder\n     22: \n     23: # LLM Model Configuration", "pattern": "api_key\\s*=\\s*[\"\\'][^\"\\']{20,}[\"\\']"}], "weak_crypto": [{"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\utils\\cache_manager.py", "line": 30, "type": "weak_crypto", "severity": "MEDIUM", "description": "Use of weak MD5 hash", "code_snippet": "     28:             temperature = round(temperature, 1)  # Округляем до 0.1\n     29:         content = f\"{prompt}|{model_name}|{temperature}\"\n>>>  30:         return hashlib.md5(content.encode()).hexdigest()\n     31: \n     32:     def _get_cache_file_path(self, cache_key: str) -> str:", "pattern": "md5\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\utils\\cache_manager.py", "line": 91, "type": "weak_crypto", "severity": "MEDIUM", "description": "Use of weak MD5 hash", "code_snippet": "     89:             \"response\": response,\n     90:             \"timestamp\": time.time(),\n>>>  91:             \"prompt_hash\": hashlib.md5(prompt.encode()).hexdigest()[:16],\n     92:             \"model\": model_name,\n     93:             \"temperature\": temperature", "pattern": "md5\\s*\\("}]}, "top_problematic_files": [["C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", 6], ["C:\\Users\\<USER>\\OpenAlpha_Evolve\\config\\settings.py", 2], ["C:\\Users\\<USER>\\OpenAlpha_Evolve\\utils\\cache_manager.py", 2], ["C:\\Users\\<USER>\\OpenAlpha_Evolve\\bugbounty_system.py", 1], ["C:\\Users\\<USER>\\OpenAlpha_Evolve\\demo_complete_bugbounty.py", 1], ["C:\\Users\\<USER>\\OpenAlpha_Evolve\\vulnerability_hunter.py", 1], ["C:\\Users\\<USER>\\OpenAlpha_Evolve\\evaluator_agent\\agent.py", 1]], "all_vulnerabilities": [{"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\bugbounty_system.py", "line": 365, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    363:         $query = \"SELECT * FROM users WHERE username = '\" . $_POST['username'] . \"'\";\n    364:         document.getElementById('search-results').innerHTML = searchQuery;\n>>> 365:         exec(\"convert \" + uploadedFile + \" thumbnail.jpg\");\n    366:         if ($_SESSION['user_id'] == $_GET['user_id']) { /* access granted */ }\n    367:         \"\"\",", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\demo_complete_bugbounty.py", "line": 50, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "     48:             $query = \"SELECT * FROM users WHERE username = '\" . $_POST['username'] . \"'\";\n     49:             document.getElementById('output').innerHTML = userInput;\n>>>  50:             exec(\"convert \" + filename + \" output.jpg\");\n     51:             \"\"\",\n     52:             scope_notes=\"Full scope testing allowed, no DoS attacks\"", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 38, "type": "sql_injection", "severity": "HIGH", "description": "Potential SQL injection via string concatenation", "code_snippet": "     36:             \"sql_injection\": [\n     37:                 {\n>>>  38:                     \"pattern\": r'[\"\\'].*\\+.*[\"\\'].*WHERE',\n     39:                     \"severity\": \"HIGH\",\n     40:                     \"description\": \"Potential SQL injection via string concatenation\"", "pattern": "[\"\\'].*\\+.*[\"\\'].*WHERE"}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 57, "type": "command_injection", "severity": "HIGH", "description": "Use of os.system() - potential command injection", "code_snippet": "     55:                     \"pattern\": r'os\\.system\\s*\\(',\n     56:                     \"severity\": \"HIGH\",\n>>>  57:                     \"description\": \"Use of os.system() - potential command injection\"\n     58:                 },\n     59:                 {", "pattern": "os\\.system\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 67, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "     65:                     \"pattern\": r'exec\\s*\\(',\n     66:                     \"severity\": \"CRITICAL\",\n>>>  67:                     \"description\": \"Use of exec() - code injection risk\"\n     68:                 },\n     69:                 {", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 357, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    355:         if \"dangerous_function\" in vuln_types:\n    356:             recommendations.append(\n>>> 357:                 \"🔧 Dangerous Functions: Избегайте использования eval(), exec() и подобных функций\"\n    358:             )\n    359:         ", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 72, "type": "command_injection", "severity": "CRITICAL", "description": "Use of eval() - code injection risk", "code_snippet": "     70:                     \"pattern\": r'eval\\s*\\(',\n     71:                     \"severity\": \"CRITICAL\", \n>>>  72:                     \"description\": \"Use of eval() - code injection risk\"\n     73:                 }\n     74:             ],", "pattern": "eval\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\self_security_audit.py", "line": 357, "type": "command_injection", "severity": "CRITICAL", "description": "Use of eval() - code injection risk", "code_snippet": "    355:         if \"dangerous_function\" in vuln_types:\n    356:             recommendations.append(\n>>> 357:                 \"🔧 Dangerous Functions: Избегайте использования eval(), exec() и подобных функций\"\n    358:             )\n    359:         ", "pattern": "eval\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\vulnerability_hunter.py", "line": 499, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    497:         $query = \"SELECT * FROM users WHERE username = '\" . $_POST['username'] . \"'\";\n    498:         document.getElementById('output').innerHTML = userInput;\n>>> 499:         exec(\"convert \" + filename + \" output.jpg\");\n    500:         \"\"\"\n    501:     )", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\config\\settings.py", "line": 19, "type": "hardcoded_secrets", "severity": "HIGH", "description": "Hardcoded API key detected", "code_snippet": "     17:     # In a real deployment, use environment variables, secrets management, or other secure methods.\n     18:     # For local testing without a .env file, you can temporarily set it like:\n>>>  19:     # GEMINI_API_KEY = \"YOUR_ACTUAL_API_KEY_HERE\"\n     20:     print(\"Warning: GEMINI_API_KEY not found in .env or environment. Using a NON-FUNCTIONAL placeholder. Please create a .env file with your valid API key.\")\n     21:     GEMINI_API_KEY = \"YOUR_API_KEY_FROM_DOTENV_WAS_NOT_FOUND_PLEASE_SET_IT_UP\" # Obvious placeholder", "pattern": "api_key\\s*=\\s*[\"\\'][^\"\\']{20,}[\"\\']"}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\config\\settings.py", "line": 21, "type": "hardcoded_secrets", "severity": "HIGH", "description": "Hardcoded API key detected", "code_snippet": "     19:     # GEMINI_API_KEY = \"YOUR_ACTUAL_API_KEY_HERE\"\n     20:     print(\"Warning: GEMINI_API_KEY not found in .env or environment. Using a NON-FUNCTIONAL placeholder. Please create a .env file with your valid API key.\")\n>>>  21:     GEMINI_API_KEY = \"YOUR_API_KEY_FROM_DOTENV_WAS_NOT_FOUND_PLEASE_SET_IT_UP\" # Obvious placeholder\n     22: \n     23: # LLM Model Configuration", "pattern": "api_key\\s*=\\s*[\"\\'][^\"\\']{20,}[\"\\']"}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\evaluator_agent\\agent.py", "line": 170, "type": "command_injection", "severity": "CRITICAL", "description": "Use of exec() - code injection risk", "code_snippet": "    168:             logger.debug(f\"Executing code: {' '.join(cmd)} in {temp_dir}\")\n    169:             start_time = time.monotonic()\n>>> 170:             proc = await asyncio.create_subprocess_exec(\n    171:                 *cmd,\n    172:                 stdout=asyncio.subprocess.PIPE,", "pattern": "exec\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\utils\\cache_manager.py", "line": 30, "type": "weak_crypto", "severity": "MEDIUM", "description": "Use of weak MD5 hash", "code_snippet": "     28:             temperature = round(temperature, 1)  # Округляем до 0.1\n     29:         content = f\"{prompt}|{model_name}|{temperature}\"\n>>>  30:         return hashlib.md5(content.encode()).hexdigest()\n     31: \n     32:     def _get_cache_file_path(self, cache_key: str) -> str:", "pattern": "md5\\s*\\("}, {"file": "C:\\Users\\<USER>\\OpenAlpha_Evolve\\utils\\cache_manager.py", "line": 91, "type": "weak_crypto", "severity": "MEDIUM", "description": "Use of weak MD5 hash", "code_snippet": "     89:             \"response\": response,\n     90:             \"timestamp\": time.time(),\n>>>  91:             \"prompt_hash\": hashlib.md5(prompt.encode()).hexdigest()[:16],\n     92:             \"model\": model_name,\n     93:             \"temperature\": temperature", "pattern": "md5\\s*\\("}], "recommendations": ["🔧 Command Injection: Используйте subprocess с списком аргументов вместо shell=True", "🔧 SQL Injection: Используйте параметризованные запросы вместо конкатенации строк", "🔧 Hardcoded Secrets: Перенесите секреты в переменные окружения или конфигурационные файлы"]}