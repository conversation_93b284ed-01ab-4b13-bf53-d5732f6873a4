#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 САМОАУДИТ БЕЗОПАСНОСТИ - OpenAlpha_Evolve Bug Bounty System
Поиск уязвимостей в собственном коде системы
"""
import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import re
import ast
import json
from datetime import datetime

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class CodeSecurityScanner:
    """Сканер безопасности кода"""
    
    def __init__(self):
        self.vulnerabilities = []
        self.scanned_files = []
        self.security_patterns = self._load_security_patterns()
    
    def _load_security_patterns(self) -> Dict[str, List[Dict]]:
        """Загрузка паттернов уязвимостей"""
        return {
            "sql_injection": [
                {
                    "pattern": r'["\'].*\+.*["\'].*WHERE',
                    "severity": "HIGH",
                    "description": "Potential SQL injection via string concatenation"
                },
                {
                    "pattern": r'execute\s*\(\s*["\'].*\+',
                    "severity": "HIGH", 
                    "description": "SQL execution with string concatenation"
                },
                {
                    "pattern": r'query\s*=\s*["\'].*\{.*\}',
                    "severity": "MEDIUM",
                    "description": "SQL query with string formatting"
                }
            ],
            "command_injection": [
                {
                    "pattern": r'os\.system\s*\(',
                    "severity": "HIGH",
                    "description": "Use of os.system() - potential command injection"
                },
                {
                    "pattern": r'subprocess\.call\s*\([^)]*shell\s*=\s*True',
                    "severity": "HIGH",
                    "description": "subprocess.call with shell=True"
                },
                {
                    "pattern": r'exec\s*\(',
                    "severity": "CRITICAL",
                    "description": "Use of exec() - code injection risk"
                },
                {
                    "pattern": r'eval\s*\(',
                    "severity": "CRITICAL", 
                    "description": "Use of eval() - code injection risk"
                }
            ],
            "path_traversal": [
                {
                    "pattern": r'open\s*\([^)]*\+',
                    "severity": "MEDIUM",
                    "description": "File open with concatenated path"
                },
                {
                    "pattern": r'\.\./',
                    "severity": "MEDIUM",
                    "description": "Potential path traversal sequence"
                }
            ],
            "hardcoded_secrets": [
                {
                    "pattern": r'password\s*=\s*["\'][^"\']{8,}["\']',
                    "severity": "HIGH",
                    "description": "Hardcoded password detected"
                },
                {
                    "pattern": r'api_key\s*=\s*["\'][^"\']{20,}["\']',
                    "severity": "HIGH",
                    "description": "Hardcoded API key detected"
                },
                {
                    "pattern": r'secret\s*=\s*["\'][^"\']{10,}["\']',
                    "severity": "HIGH",
                    "description": "Hardcoded secret detected"
                }
            ],
            "unsafe_deserialization": [
                {
                    "pattern": r'pickle\.loads?\s*\(',
                    "severity": "HIGH",
                    "description": "Unsafe pickle deserialization"
                },
                {
                    "pattern": r'yaml\.load\s*\([^)]*Loader\s*=\s*yaml\.Loader',
                    "severity": "HIGH",
                    "description": "Unsafe YAML loading"
                }
            ],
            "weak_crypto": [
                {
                    "pattern": r'md5\s*\(',
                    "severity": "MEDIUM",
                    "description": "Use of weak MD5 hash"
                },
                {
                    "pattern": r'sha1\s*\(',
                    "severity": "MEDIUM", 
                    "description": "Use of weak SHA1 hash"
                }
            ],
            "information_disclosure": [
                {
                    "pattern": r'print\s*\([^)]*password',
                    "severity": "MEDIUM",
                    "description": "Potential password disclosure in logs"
                },
                {
                    "pattern": r'logger\.[^(]*\([^)]*api_key',
                    "severity": "MEDIUM",
                    "description": "Potential API key disclosure in logs"
                }
            ]
        }
    
    async def scan_codebase(self) -> Dict[str, Any]:
        """Сканирование всей кодовой базы"""
        
        logger.info("🔍 === НАЧАЛО САМОАУДИТА БЕЗОПАСНОСТИ ===")
        
        # Получаем список Python файлов
        python_files = self._get_python_files()
        
        logger.info(f"📁 Найдено {len(python_files)} Python файлов для сканирования")
        
        # Сканируем каждый файл
        for file_path in python_files:
            await self._scan_file(file_path)
        
        # Анализируем результаты
        report = self._generate_security_report()
        
        logger.info("✅ === САМОАУДИТ ЗАВЕРШЕН ===")
        
        return report
    
    def _get_python_files(self) -> List[Path]:
        """Получение списка Python файлов"""
        
        python_files = []
        current_dir = Path.cwd()
        
        # Исключаемые директории
        exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'venv', 'env', 'node_modules'}
        
        for file_path in current_dir.rglob("*.py"):
            # Проверяем, что файл не в исключаемых директориях
            if not any(excluded in file_path.parts for excluded in exclude_dirs):
                python_files.append(file_path)
        
        return python_files
    
    async def _scan_file(self, file_path: Path):
        """Сканирование отдельного файла"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.scanned_files.append(str(file_path))
            
            # Сканируем на различные типы уязвимостей
            for vuln_type, patterns in self.security_patterns.items():
                for pattern_info in patterns:
                    matches = re.finditer(pattern_info["pattern"], content, re.IGNORECASE | re.MULTILINE)
                    
                    for match in matches:
                        line_number = content[:match.start()].count('\n') + 1
                        
                        vulnerability = {
                            "file": str(file_path),
                            "line": line_number,
                            "type": vuln_type,
                            "severity": pattern_info["severity"],
                            "description": pattern_info["description"],
                            "code_snippet": self._get_code_snippet(content, line_number),
                            "pattern": pattern_info["pattern"]
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        
                        logger.warning(
                            f"🚨 {pattern_info['severity']}: {pattern_info['description']} "
                            f"в {file_path}:{line_number}"
                        )
            
            # Дополнительные проверки AST
            await self._ast_security_check(file_path, content)
            
        except Exception as e:
            logger.error(f"❌ Ошибка сканирования {file_path}: {e}")
    
    def _get_code_snippet(self, content: str, line_number: int, context: int = 2) -> str:
        """Получение фрагмента кода вокруг уязвимости"""
        
        lines = content.split('\n')
        start = max(0, line_number - context - 1)
        end = min(len(lines), line_number + context)
        
        snippet_lines = []
        for i in range(start, end):
            prefix = ">>> " if i == line_number - 1 else "    "
            snippet_lines.append(f"{prefix}{i+1:3d}: {lines[i]}")
        
        return '\n'.join(snippet_lines)
    
    async def _ast_security_check(self, file_path: Path, content: str):
        """Проверка безопасности через AST анализ"""
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                # Проверка на опасные функции
                if isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        func_name = node.func.id
                        
                        dangerous_functions = {
                            'eval': 'CRITICAL',
                            'exec': 'CRITICAL', 
                            'compile': 'HIGH',
                            '__import__': 'HIGH'
                        }
                        
                        if func_name in dangerous_functions:
                            vulnerability = {
                                "file": str(file_path),
                                "line": node.lineno,
                                "type": "dangerous_function",
                                "severity": dangerous_functions[func_name],
                                "description": f"Use of dangerous function: {func_name}()",
                                "code_snippet": f"Line {node.lineno}: {func_name}() call detected",
                                "pattern": f"AST analysis: {func_name}"
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                
                # Проверка на небезопасные импорты
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in ['pickle', 'cPickle']:
                            vulnerability = {
                                "file": str(file_path),
                                "line": node.lineno,
                                "type": "unsafe_import",
                                "severity": "MEDIUM",
                                "description": f"Import of potentially unsafe module: {alias.name}",
                                "code_snippet": f"Line {node.lineno}: import {alias.name}",
                                "pattern": f"AST analysis: import {alias.name}"
                            }
                            
                            self.vulnerabilities.append(vulnerability)
        
        except SyntaxError:
            # Файл с синтаксическими ошибками пропускаем
            pass
        except Exception as e:
            logger.debug(f"AST анализ не удался для {file_path}: {e}")
    
    def _generate_security_report(self) -> Dict[str, Any]:
        """Генерация отчета по безопасности"""
        
        # Группировка по типам уязвимостей
        vuln_by_type = {}
        vuln_by_severity = {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0}
        
        for vuln in self.vulnerabilities:
            vuln_type = vuln["type"]
            severity = vuln["severity"]
            
            if vuln_type not in vuln_by_type:
                vuln_by_type[vuln_type] = []
            vuln_by_type[vuln_type].append(vuln)
            
            vuln_by_severity[severity] += 1
        
        # Топ-10 самых проблемных файлов
        file_vuln_count = {}
        for vuln in self.vulnerabilities:
            file_path = vuln["file"]
            file_vuln_count[file_path] = file_vuln_count.get(file_path, 0) + 1
        
        top_files = sorted(file_vuln_count.items(), key=lambda x: x[1], reverse=True)[:10]
        
        report = {
            "scan_summary": {
                "timestamp": datetime.now().isoformat(),
                "files_scanned": len(self.scanned_files),
                "total_vulnerabilities": len(self.vulnerabilities),
                "severity_breakdown": vuln_by_severity
            },
            "vulnerabilities_by_type": vuln_by_type,
            "top_problematic_files": top_files,
            "all_vulnerabilities": self.vulnerabilities,
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Генерация рекомендаций по исправлению"""
        
        recommendations = []
        
        # Анализируем найденные уязвимости и даем рекомендации
        vuln_types = set(vuln["type"] for vuln in self.vulnerabilities)
        
        if "command_injection" in vuln_types:
            recommendations.append(
                "🔧 Command Injection: Используйте subprocess с списком аргументов вместо shell=True"
            )
        
        if "sql_injection" in vuln_types:
            recommendations.append(
                "🔧 SQL Injection: Используйте параметризованные запросы вместо конкатенации строк"
            )
        
        if "hardcoded_secrets" in vuln_types:
            recommendations.append(
                "🔧 Hardcoded Secrets: Перенесите секреты в переменные окружения или конфигурационные файлы"
            )
        
        if "unsafe_deserialization" in vuln_types:
            recommendations.append(
                "🔧 Unsafe Deserialization: Используйте безопасные форматы сериализации (JSON, YAML с safe_load)"
            )
        
        if "dangerous_function" in vuln_types:
            recommendations.append(
                "🔧 Dangerous Functions: Избегайте использования eval(), exec() и подобных функций"
            )
        
        return recommendations
    
    def print_security_summary(self, report: Dict[str, Any]):
        """Вывод резюме безопасности"""
        
        summary = report["scan_summary"]
        
        logger.info("\n🔍 === РЕЗЮМЕ САМОАУДИТА БЕЗОПАСНОСТИ ===")
        logger.info(f"📁 Просканировано файлов: {summary['files_scanned']}")
        logger.info(f"🚨 Найдено уязвимостей: {summary['total_vulnerabilities']}")
        
        logger.info("\n📊 По критичности:")
        for severity, count in summary["severity_breakdown"].items():
            if count > 0:
                emoji = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🟢"}
                logger.info(f"   {emoji.get(severity, '⚪')} {severity}: {count}")
        
        if report["vulnerabilities_by_type"]:
            logger.info("\n🎯 По типам уязвимостей:")
            for vuln_type, vulns in report["vulnerabilities_by_type"].items():
                logger.info(f"   • {vuln_type.replace('_', ' ').title()}: {len(vulns)}")
        
        if report["top_problematic_files"]:
            logger.info("\n📋 Топ-5 проблемных файлов:")
            for file_path, count in report["top_problematic_files"][:5]:
                short_path = str(Path(file_path).name)
                logger.info(f"   • {short_path}: {count} уязвимостей")
        
        if report["recommendations"]:
            logger.info("\n💡 Рекомендации:")
            for rec in report["recommendations"]:
                logger.info(f"   {rec}")
    
    def save_report(self, report: Dict[str, Any], filename: str = "security_audit_report.json"):
        """Сохранение отчета"""
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Отчет сохранен: {filename}")
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения отчета: {e}")

async def main():
    """Главная функция самоаудита"""
    
    logger.info("🎯 Запуск самоаудита безопасности OpenAlpha_Evolve Bug Bounty System")
    
    try:
        # Создаем сканер
        scanner = CodeSecurityScanner()
        
        # Запускаем сканирование
        report = await scanner.scan_codebase()
        
        # Выводим резюме
        scanner.print_security_summary(report)
        
        # Сохраняем отчет
        scanner.save_report(report)
        
        # Дополнительная информация
        if report["scan_summary"]["total_vulnerabilities"] == 0:
            logger.info("🎉 Поздравляем! Критичных уязвимостей не найдено!")
        else:
            logger.info("🔧 Рекомендуется исправить найденные уязвимости")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Критическая ошибка самоаудита: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
